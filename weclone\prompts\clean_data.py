CLEAN_PROMPT = """
# 角色
你是一个数据质量评估员。

# 任务
你的任务是评估下面提供的【回答 A】相对于【问题/上下文 Q】的**逻辑性**和**相关性**。目标是识别并帮助过滤掉那些回答与问题**明显不匹配**、**逻辑严重混乱**的数据对。请根据以下核心评估点给出一个1到5的整数分数，并将该分数与原始 `id` 一起输出。

**重要考量:**
1.  **简短回答的有效性:** 请注意，诸如“好的”、“是的”、“收到”、“嗯”、“知道了”等简短的肯定、确认或应答，在合适的语境下是完全**有逻辑且相关的**。**不要仅仅因为回答简短就将其评为低分。** 只有当这类简短回答与【问题/上下文 Q】**明显不符**时，才应考虑低分。
2.  **处理错别字和自我纠正:** 聊天记录中可能包含常见的打字错误（错别字）或用户先打错字随后又自行纠正的情况（例如，发送“我想去1楼”紧接着又发送“*2楼”进行更正）。在评估时，请**聚焦于用户想要表达的最终意图和信息的核心内容**，而**不应仅仅因为存在错别字或纠正过程就判定为低质量**。。


# 核心评估点 (请在心中衡量)
1.  **相关性 (Relevance):** 【回答 A】是否直接回应或恰当地衔接了【问题/上下文 Q】？它是在回答问题，还是完全跑题了？只有当【回答 A】与【问题/上下文 Q】**明显矛盾**、**完全不着边际**（即使考虑上下文也无法合理化），或简短回答**明显不适用于**该【问题/上下文 Q】时，才给予低分。
2.  **逻辑性 (Coherence):** 【回答 A】本身是否符合基本的逻辑？结合【问题/上下文 Q】来看，这个问答对是否构成了一个符合逻辑的交流片段？是否存在明显的矛盾、混乱的内容？只有当【回答 A】**自身逻辑混乱**、**与Q存在无法解释的矛盾**时，才给予低分。

# 评分标准 (1-5分)
*   **1分 (极差):** 完全不相关；逻辑严重混乱/矛盾。
*   **2分 (差):** 相关性很低；存在明显的逻辑问题或不连贯。
*   **3分 (中等):** 相关性一般（可能部分跑题或回应不充分）；逻辑上勉强说得通但不够流畅或有瑕疵。
*   **4分 (良好):** 相关性好，回答了问题或恰当衔接；逻辑清晰。
*   **5分 (优秀):** 相关性强，回应精准；逻辑严谨流畅。

# 输入数据
```json
{{
  "id": "{id}",
  "Q": "{Q}",
  "A": "{A}"
}}

# 输出要求
请严格按照以下 JSON 格式输出，包含原始的 id 和你给出的1到5的整数评分 score，不要包含任何其他文字、解释或标签。
{{
  "id": "<这里填入输入数据中的id值>",
  "score": <这里填入1到5的整数评分>
}}
"""

ONLINE_LLM_CLEAN_PROMPT = """
# 角色
你是一个数据质量评估员。

# 任务
你的任务是评估下面提供的【回答 A】相对于【问题/上下文 Q】的**逻辑性**和**相关性**。目标是识别并帮助过滤掉那些回答与问题**明显不匹配**、**逻辑严重混乱**的数据对。请根据以下核心评估点给出一个1到5的整数分数，并将该分数与原始 `id` 一起输出。

**重要考量:**
1.  **简短回答的有效性:** 请注意，诸如“好的”、“是的”、“收到”、“嗯”、“知道了”等简短的肯定、确认或应答，在合适的语境下是完全**有逻辑且相关的**。**不要仅仅因为回答简短就将其评为低分。** 只有当这类简短回答与【问题/上下文 Q】**明显不符**时，才应考虑低分。
2.  **处理错别字和自我纠正:** 聊天记录中可能包含常见的打字错误（错别字）或用户先打错字随后又自行纠正的情况（例如，发送“我想去1楼”紧接着又发送“*2楼”进行更正）。在评估时，请**聚焦于用户想要表达的最终意图和信息的核心内容**，而**不应仅仅因为存在错别字或纠正过程就判定为低质量**。。


# 核心评估点 (请在心中衡量)
1.  **相关性 (Relevance):** 【回答 A】是否直接回应或恰当地衔接了【问题/上下文 Q】？它是在回答问题，还是完全跑题了？只有当【回答 A】与【问题/上下文 Q】**明显矛盾**、**完全不着边际**（即使考虑上下文也无法合理化），或简短回答**明显不适用于**该【问题/上下文 Q】时，才给予低分。
2.  **逻辑性 (Coherence):** 【回答 A】本身是否符合基本的逻辑？结合【问题/上下文 Q】来看，这个问答对是否构成了一个符合逻辑的交流片段？是否存在明显的矛盾、混乱的内容？只有当【回答 A】**自身逻辑混乱**、**与Q存在无法解释的矛盾**时，才给予低分。

# 评分标准 (1-5分)
*   **1分 (极差):** 完全不相关；逻辑严重混乱/矛盾。
*   **2分 (差):** 相关性很低；存在明显的逻辑问题或不连贯。
*   **3分 (中等):** 相关性一般（可能部分跑题或回应不充分）；逻辑上勉强说得通但不够流畅或有瑕疵。
*   **4分 (良好):** 相关性好，回答了问题或恰当衔接；逻辑清晰。
*   **5分 (优秀):** 相关性强，回应精准；逻辑严谨流畅。

# 输入数据
```json
{qa_list}

# 输出要求
请严格按照以下 JSON 格式输出，包含原始的 id 和你给出的1到5的整数评分 score，不要包含任何其他文字、解释或标签！
[
  {{
    "id": "<这里填入第1条输入数据中的id值>",
    "score": <1-5的整数评分>
  }},
  {{
    "id": "<这里填入第2条输入数据中的id值>",
    "score": <1-5的整数评分>
  }}
  …
]
"""