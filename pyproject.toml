[project]
name = "WeClone"
version = "0.2.21"
description = "从聊天记录创造数字分身的一站式解决方案"
authors = [{ name = "xming521" }]
readme = "README.md"
requires-python = ">=3.10,<3.14"

dependencies = [
  "pandas",
  "commentjson",
  "click",
  "pydantic==2.10.6",
  "setuptools>=78.1.0",
  "loguru>=0.7.3",
  "torch>=2.6.0",
  "transformers==4.49.0",
  "tomli; python_version < '3.11'",
  "langchain",
]

[tool.weclone]
# 配置文件的版本号，当配置文件结构或重要默认值发生变化时，应增加此版本号
config_version = "0.2.21"

# 配置文件更新日志
config_changelog = """
[0.2.1] - 2025-04-29 - 初始配置版本。
[0.2.2] - 2025-05-01 - 增加llm清洗数据配置,blocked_words迁移到settings.jsonc统一配置文件。
[0.2.21] - 2025-05-01 - 增加在线llm清洗数据配置，兼容openai风格接口。
"""

[dependency-groups]
# xcodec = ["xcodec2==0.1.3"]
sparktts = [
  "einops>=0.8.1",
  "einx>=0.3.0",
  "numpy==1.26.4",
  "omegaconf>=2.3.0",
  "packaging>=24.2",
  "safetensors>=0.5.2",
  "soundfile>=0.12.1",
  "soxr>=0.5.0.post1",
  "torchaudio>=2.6.0",
  "tqdm>=4.66.5",
]
main = [
  "llamafactory>=0.9.2",
  "openai==1.76.0",
  "vllm==0.8.2; platform_system == 'Linux'",
]
dev = ["pytest", "pytest-order", "pyright", "ruff"]

[project.scripts]
weclone-cli = "weclone.cli:cli"

[tool.uv]
conflicts = [
  # [{ group = "wx" }, { group = "xcodec" }],
]

[tool.uv.sources]
torch = [
  { index = "pytorch-cu124", marker = "platform_system == 'Windows'" },
  { index = "pytorch-cu124", marker = "platform_system == 'Linux'" },
]
torchaudio = [
  { index = "pytorch-cu124", marker = "platform_system == 'Windows'" },
  { index = "pytorch-cu124", marker = "platform_system == 'Linux'" },
]
torchvision = [
  { index = "pytorch-cu124", marker = "platform_system == 'Windows'" },
  { index = "pytorch-cu124", marker = "platform_system == 'Linux'" },
]


[[tool.uv.index]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
default = true

[[tool.uv.index]]
name = "pytorch-cu124"
url = "https://download.pytorch.org/whl/cu124"
explicit = true

[tool.setuptools.packages.find]
where = ["."]                      # 表示在项目根目录开始查找
include = ["weclone*"]             # 只包含名为 weclone 的目录及其子包
exclude = ["*tests*", "*archive*"] # 可以选择性排除其他模式，比如测试目录


[tool.pyright]
typeCheckingMode = "basic"
include = ["weclone/data"]
exclude = ["**/archive", "**/tests"]
ignore = ["**/archive"]

reportMissingImports = "error"
reportMissingTypeStubs = false

pythonVersion = "3.10"
pythonPlatform = "Linux"

[tool.ruff]
exclude = [
  "**/archive",
  "**/tests",
  "weclone-audio/src/server未完工",
  "weclone-audio/src/Spark-TTS",
]
line-length = 120

lint.ignore = ["F403", "F405", "E501", "E402"]
lint.select = [
  "F",     # Pyflakes
  "W",     # pycodestyle warnings
  "E",     # pycodestyle errors
  "ASYNC", # flake8-async
  "C4",    # flake8-comprehensions
  "Q",     # flake8-quotes
]
target-version = "py310"

[tool.pytest.ini_options]
addopts = "-x"
